"""
Simplified event creation service for v2 API.
This service maintains the same transactional behavior as the v1 API
while providing a cleaner, more maintainable implementation.
"""
import logging
from typing import Dict, List, Any, Tuple, Optional
from django.db import transaction
from django.utils import timezone
from authentication.models import User
from django.core.exceptions import ValidationError as DjangoValidationError
from rest_framework.exceptions import ValidationError as DRFValidationError

logger = logging.getLogger(__name__)


class EventCreationResult:
    """Simple result object for event creation operations"""

    def __init__(self, success: bool, events: List = None, errors: List = None, 
                 warnings: List = None, execution_time_ms: float = 0.0):
        self.success = success
        self.events = events or []
        self.errors = errors or []
        self.warnings = warnings or []
        self.execution_time_ms = execution_time_ms


class EventCreationServiceV2:
    """
    Simplified event creation service that maintains v1 API functional parity.

    This service handles the complete event creation flow in a single atomic transaction:
    1. FIFO validation
    2. Routing validation
    3. Event transformation
    4. Event persistence
    5. Routing execution updates
    6. FIFO violation logging

    All operations are atomic - if any step fails, no database changes are made.
    """

    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

    def create_events(self, event_data: Dict[str, Any], user: User) -> EventCreationResult:
        """
        Creates manufacturing events with the same behavior as the v1 API.

        Args:
            event_data: Raw event data from the request
            user: user creating the event

        Returns:
            EventCreationResult with success status and created events or errors
        """
        import time
        start_time = time.time()

        try:
            self.logger.info(f"Starting event creation for user {user}")

            # Step 0: Parse serial number and cache the result at the very beginning
            serial_number = event_data.get('serial_number')
            form_id = event_data.get('form')
            
            if not serial_number:
                return EventCreationResult(
                    success=False,
                    errors=[{"field": "serial_number", "message": "Serial number is required"}],
                    execution_time_ms=(time.time() - start_time) * 1000
                )
            
            if not form_id:
                return EventCreationResult(
                    success=False,
                    errors=[{"field": "form", "message": "Form ID is required"}],
                    execution_time_ms=(time.time() - start_time) * 1000
                )

            # Parse serial number with product type from form's process block
            try:
                self._parse_and_cache_serial_number(serial_number, form_id, user)
            except Exception as e:
                self.logger.error(f"Serial number parsing failed: {str(e)}")
                return EventCreationResult(
                    success=False,
                    errors=[{"field": "serial_number", "message": f"Serial number parsing failed: {str(e)}"}],
                    execution_time_ms=(time.time() - start_time) * 1000
                )

            # Validate product type consistency between product and form
            try:
                self._validate_product_type_consistency(serial_number, form_id)
            except Exception as e:
                self.logger.error(f"Product type validation failed: {str(e)}")
                return EventCreationResult(
                    success=False,
                    errors=[{"field": "product_type", "message": str(e)}],
                    execution_time_ms=(time.time() - start_time) * 1000
                )

            # Use atomic transaction to ensure all-or-nothing behavior
            with transaction.atomic():
                # Step 1: FIFO Validation
                fifo_valid, fifo_message = self._validate_fifo(event_data)
                if not fifo_valid:
                    self.logger.warning(f"FIFO validation failed: {fifo_message}")
                    return EventCreationResult(
                        success=False,
                        errors=[{"field": "fifo", "message": fifo_message}],
                        execution_time_ms=(time.time() - start_time) * 1000
                    )

                # # Step 1.5: Routing Validation
                # NOT NECESSARY here since the same is being done in the _transform_event_data step 2

                # Step 2: Transform event data
                try:
                    transformed_events = self._transform_event_data(event_data)
                    if not transformed_events:
                        return EventCreationResult(
                            success=False,
                            errors=[{"field": "transformation", "message": "Event transformation failed"}],
                            execution_time_ms=(time.time() - start_time) * 1000
                        )
                except DjangoValidationError as e:
                    self.logger.error(f"Event transformation validation error: {str(e)}")
                    return EventCreationResult(
                        success=False,
                        errors=[{"field": "transformation", "message": str(e)}],
                        execution_time_ms=(time.time() - start_time) * 1000
                    )
                except Exception as e:
                    self.logger.error(f"Event transformation error: {str(e)}")
                    return EventCreationResult(
                        success=False,
                        errors=[{"field": "transformation", "message": "Event transformation failed, check logs for details"}],
                        execution_time_ms=(time.time() - start_time) * 1000
                    )

                # Step 3: Create events
                created_events = self._create_events(transformed_events, user)

                # Step 4: Update routing execution
                self._update_routing_execution(created_events)

                # Step 5: Update FIFO violation logs
                # NOT NECESSARY here since the same is being done in the validate_event_with_fifo

                execution_time = (time.time() - start_time) * 1000
                self.logger.info(f"Event creation completed successfully in {execution_time:.2f}ms")

                warnings = []
                if fifo_message:  # FIFO warning (allowed to proceed)
                    warnings.append({"field": "fifo", "message": fifo_message})

                return EventCreationResult(
                    success=True,
                    events=created_events,
                    warnings=warnings,
                    execution_time_ms=execution_time
                )

        except Exception as e:
            self.logger.error(f"Event creation failed: {str(e)}", exc_info=True)
            return EventCreationResult(
                success=False,
                errors=[{"field": "system", "message": f"Event creation failed: {str(e)}"}],
                execution_time_ms=(time.time() - start_time) * 1000
            )

    def validate_events(self, event_data: Dict[str, Any]) -> EventCreationResult:
        """
        Validates event data without creating events.

        Args:
            event_data: Raw event data to validate

        Returns:
            EventCreationResult with validation results
        """
        import time
        start_time = time.time()

        try:
            self.logger.info("Starting event validation")

            errors = []
            warnings = []

            # Step 0: Parse serial number and cache the result at the very beginning
            serial_number = event_data.get('serial_number')
            form_id = event_data.get('form')
            
            if not serial_number:
                errors.append({"field": "serial_number", "message": "Serial number is required"})
            
            if not form_id:
                errors.append({"field": "form", "message": "Form ID is required"})
                
            # If we have both serial number and form, try to parse and cache
            if serial_number and form_id:
                try:
                    # Create a dummy user for validation (we don't have a real user in validation)
                    from authentication.models import User
                    dummy_user = User(id=1)  # Just for context, not saved
                    self._parse_and_cache_serial_number(serial_number, form_id, dummy_user)

                    # Validate product type consistency
                    try:
                        self._validate_product_type_consistency(serial_number, form_id)
                    except Exception as e:
                        self.logger.error(f"Product type validation failed during validation: {str(e)}")
                        errors.append({"field": "product_type", "message": str(e)})

                except Exception as e:
                    self.logger.error(f"Serial number parsing failed during validation: {str(e)}")
                    errors.append({"field": "serial_number", "message": f"Serial number parsing failed: {str(e)}"})

            # If we already have errors, return early
            if errors:
                return EventCreationResult(
                    success=False,
                    errors=errors,
                    execution_time_ms=(time.time() - start_time) * 1000
                )

            # FIFO validation
            fifo_valid, fifo_message = self._validate_fifo(event_data)
            if not fifo_valid:
                errors.append({"field": "fifo", "message": fifo_message})
            elif fifo_message:
                warnings.append({"field": "fifo", "message": fifo_message})

            # Basic schema validation
            schema_errors = self._validate_schema(event_data)
            errors.extend(schema_errors)

            # Routing validation (without database updates)
            routing_errors = self._validate_routing_only(event_data)
            errors.extend(routing_errors)

            execution_time = (time.time() - start_time) * 1000
            is_valid = len(errors) == 0

            self.logger.info(f"Event validation completed: valid={is_valid}, "
                           f"errors={len(errors)}, warnings={len(warnings)}")

            return EventCreationResult(
                success=is_valid,
                errors=errors,
                warnings=warnings,
                execution_time_ms=execution_time
            )

        except Exception as e:
            self.logger.error(f"Event validation failed: {str(e)}", exc_info=True)
            return EventCreationResult(
                success=False,
                errors=[{"field": "system", "message": f"Validation failed: {str(e)}"}],
                execution_time_ms=(time.time() - start_time) * 1000
            )

    def _validate_fifo(self, event_data: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """FIFO validation using existing v1 logic"""
        try:
            from operation.services.fifo_validation_service import validate_event_with_fifo
            return validate_event_with_fifo(event_data)
        except Exception as e:
            self.logger.error(f"FIFO validation error: {str(e)}")
            return False, f"FIFO validation failed: {str(e)}"

    def _validate_routing(self, event_data: Dict[str, Any]) -> Tuple[bool, List[Dict[str, str]]]:
        """Routing validation using existing v1 logic"""
        try:
            from workflow_config.services.routing_validation_service import RoutingValidationService

            is_valid, validation_errors, next_executable = RoutingValidationService.validate_event(event_data)

            # Convert validation errors to our format
            errors = []
            for error in validation_errors or []:
                errors.append({
                    "field": error.get('field', 'routing'),
                    "message": error.get('error', 'Routing validation failed')
                })

            return is_valid, errors

        except Exception as e:
            self.logger.error(f"Routing validation error: {str(e)}")
            return False, [{"field": "routing", "message": f"Routing validation failed: {str(e)}"}]

    def _validate_schema(self, event_data: Dict[str, Any]) -> List[Dict[str, str]]:
        """Basic schema validation"""
        errors = []

        required_fields = ['form', 'serial_number', 'event_data', 'timestamp']
        for field in required_fields:
            if field not in event_data or event_data[field] is None:
                errors.append({
                    "field": field,
                    "message": f"Required field '{field}' is missing or null"
                })

        return errors

    def _validate_routing_only(self, event_data: Dict[str, Any]) -> List[Dict[str, str]]:
        """Routing validation without database updates"""
        try:
            from workflow_config.services.routing_validation_service import RoutingValidationService

            # This will validate but also update the database, which we don't want for validation-only
            # For now, we'll skip routing validation in validation-only mode
            # In a production system, we'd need a separate validation-only method
            return []

        except Exception as e:
            self.logger.error(f"Routing validation error: {str(e)}")
            return [{"field": "routing", "message": f"Routing validation failed: {str(e)}"}]

    def _transform_event_data(self, event_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Transform event data using existing v1 logic"""
        from operation.services.event_service import ManufacturingEventService
        
        # Serial number is already parsed and cached at the beginning of the request
        # No need to add context here as the transformer will use cached data
        return ManufacturingEventService.transform_event_data(event_data)

    def _create_events(self, transformed_events: List[Dict[str, Any]], user: User) -> List:
        """Create events using Django serializers"""
        try:
            from operation.serializers.event_serializers import ManufacturingEventSerializer

            # Add user_id to each event
            for event_data in transformed_events:
                event_data['created_by_id'] = user.id

            serializer = ManufacturingEventSerializer(data=transformed_events, many=True)
            serializer.is_valid(raise_exception=True)
            return serializer.save(created_by=user)

        except Exception as e:
            self.logger.error(f"Event creation error: {str(e)}")
            raise

    def _update_routing_execution(self, created_events: List) -> None:
        """Update routing execution using existing v1 logic"""
        try:
            from workflow_config.services.routing_validation_service import RoutingValidationService

            # Group events by serial number
            events_by_serial = {}
            for event in created_events:
                if event.serial_number not in events_by_serial:
                    events_by_serial[event.serial_number] = []
                events_by_serial[event.serial_number].append(event.id)

            # Update routing execution for each serial number
            for serial_number, event_ids in events_by_serial.items():
                RoutingValidationService.update_routing_execution_with_event_ids(
                    serial_number, event_ids
                )

        except Exception as e:
            self.logger.error(f"Routing execution update error: {str(e)}")
            raise

    def _update_fifo_violation_logs(self, created_events: List) -> None:
        """Update FIFO violation logs using existing v1 logic"""
        try:
            from operation.models import FIFOViolationLog

            # Update FIFO violation logs
            for event in created_events:
                FIFOViolationLog.objects.filter(
                    serial_number=event.serial_number,
                    event__isnull=True
                ).update(event=event)

        except Exception as e:
            self.logger.error(f"FIFO violation log update error: {str(e)}")
            # Don't raise - this is not critical
            pass

    def _parse_and_cache_serial_number(self, serial_number: str, form_id: int, user: User) -> None:
        """
        Parse serial number with product type from form's process block and cache the result.
        This should be called at the very beginning of event processing.

        Args:
            serial_number: The serial number to parse
            form_id: The form ID to get product type from
            user: The user creating the event

        Raises:
            Exception: If serial number parsing fails
        """
        from operation.services.event_service import ManufacturingEventService
        from operation.services import serial_number_cache

        self.logger.info(f"Parsing and caching serial number: {serial_number} with form ID: {form_id}")

        # Check if already cached
        cached_data = serial_number_cache.get_parsed_serial_data(serial_number)
        if cached_data:
            self.logger.info(f"Serial number {serial_number} already cached, skipping parsing")
            return

        # Create comprehensive context
        context = {
            'form_id': form_id,
            'user_id': user.id,
            'timestamp': timezone.now().isoformat(),
            'service': 'event_creation_service_v2'
        }

        # Parse serial number with product type from form's process block
        processed_data = ManufacturingEventService.process_serial_number(
            serial_number=serial_number,
            context=context,
            form_id=form_id
        )

        if not processed_data:
            raise ValueError("Serial number processing returned no data")

        self.logger.info(f"Successfully parsed and cached serial number: {serial_number}")
        self.logger.debug(f"Cached data keys: {list(processed_data.keys())}")

    def _validate_product_type_consistency(self, serial_number: str, form_id: int) -> None:
        """
        Validate that the product's type_id matches the form's process_block.product_type.

        Args:
            serial_number: The serial number to get product information for
            form_id: The form ID to get process block product type from

        Raises:
            ValueError: If product types don't match or required data is missing
        """
        from operation.services import serial_number_cache
        from workflow_config.models import FormConfig

        self.logger.info(f"Validating product type consistency for serial number: {serial_number}, form ID: {form_id}")

        # Get product from cached data
        cached_data = serial_number_cache.get_parsed_serial_data(serial_number)
        if not cached_data or 'product' not in cached_data:
            raise ValueError("Product information not found in cached data")

        product = cached_data['product']
        product_type_id = getattr(product, 'type_id', None)

        if not product_type_id:
            raise ValueError("Product does not have a product type assigned")

        # Get form and its process block product type
        try:
            form_config = FormConfig.objects.select_related('process_block__product_type').get(id=form_id)
        except FormConfig.DoesNotExist:
            raise ValueError(f"Form with ID {form_id} not found")

        if not form_config.process_block:
            raise ValueError("Form is not associated with a process block")

        process_block_product_type = form_config.process_block.product_type

        if not process_block_product_type:
            raise ValueError("Process block does not have a product type assigned")

        # Compare product types
        if product_type_id.id != process_block_product_type.id:
            self.logger.error(
                f"Product type mismatch: Product type '{product_type_id.label}' "
                f"does not match form's process block product type '{process_block_product_type.label}'"
            )
            raise ValueError(
                "Wrong form for this serial number. "
                f"Selected form is for work station: {form_config.process_block.name}(type: {process_block_product_type.label}) "
                f"but product is of type '{product_type_id.label}'. Please choose the right one."
            )

        self.logger.info(
            f"Product type validation successful: Both product and form use product type '{product_type_id.code}'"
        )
